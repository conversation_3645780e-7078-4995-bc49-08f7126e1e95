{
  "[json]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[markdown]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "jest.runMode": {
    "type": "on-demand",
  },
  "jest.jestCommandLine": "node_modules/.bin/jest --testTimeout=100000000",
  "diffEditor.ignoreTrimWhitespace": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.formatOnSave": true,
  "files.eol": "\n",
  "testExplorer.useNativeTesting": true,
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.implementationsCodeLens.enabled": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.referencesCodeLens.enabled": true,
  "typescript.referencesCodeLens.showOnAllFunctions": false,
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": false,

    "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.preferences.displayPartsForJSDoc": true,
  "typescript.preferences.quoteStyle": "double",
  "typescript.preferences.useLabelDetailsInCompletionEntries": true,
  
  // ⭐ KLUCZOWE - pokaż więcej szczegółów w hover
  "typescript.format.insertSpaceAfterTypeAssertion": true,
  "typescript.format.placeOpenBraceOnNewLineForControlBlocks": false,
  "typescript.format.placeOpenBraceOnNewLineForFunctions": false,
  
  // Pokaż inlay hints
  "typescript.inlayHints.parameterNames.enabled": "literals",
  "typescript.inlayHints.parameterTypes.enabled": true,
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.inlayHints.propertyDeclarationTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,
  "typescript.inlayHints.enumMemberValues.enabled": true
}