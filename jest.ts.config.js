/**
 * This is the Jest configuration file for unit tests ran directly from TS files.
 */
/** @type {import('ts-jest/dist/types').InitialOptionsTsJest} */
module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    silent: false,
    testPathIgnorePatterns: ['.d.(ts|js)$','.helper.(ts|js)$', 'dist/src/__tests__/'],
    reporters: ['jest-standard-reporter', 'jest-skipped-reporter', 'jest-failure-reporter','jest-junit', ],
    coverageReporters: ['json', 'text'],
    setupFiles: ['<rootDir>/src/__tests__/setup.ts']
};
