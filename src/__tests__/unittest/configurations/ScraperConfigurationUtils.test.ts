import {ScraperConfiguration, ScraperConfigurationStatus} from '../../../configurations/ScraperConfiguration';
import {convertToPublicScraperConfiguration} from '../../../configurations/ScraperConfigurationUtils';
import {Source} from '../../../types';
import {Deferred} from '../../../utils/Deferred';
import {createRun, createRunManager, testWithRunManagerContext} from '../../utils/RunUtils';
import {TestJob} from '../../utils/TestJob';

describe('convertToPublicScraperConfiguration should', () => {
    it('convert scraper configuration status with run manager', async () => {
        await testWithRunManagerContext(async (runManager) => {
            const expectedRunningScraperConfigurationMock = {source: Source.NINTENDO_SALES, status: ScraperConfigurationStatus.CONFIGURED} as ScraperConfiguration;
            const expectedScheduledScraperConfigurationMock = {source: Source.STEAM_SALES, status: ScraperConfigurationStatus.CONFIGURED} as ScraperConfiguration;

            const job1 = new TestJob(async () => {
                await new Deferred().promise;
            });

            const run1 = createRun([job1], {source: Source.NINTENDO_SALES, isShadowRun: false});
            const run2 = createRun([job1], {source: Source.STEAM_SALES, isShadowRun: false});

            runManager.addRun(run1).catch(void 0);
            runManager.addRun(run2).catch(void 0);

            const result = convertToPublicScraperConfiguration(expectedRunningScraperConfigurationMock, runManager);
            const result2 = convertToPublicScraperConfiguration(expectedScheduledScraperConfigurationMock, runManager);

            expect(result.status).toEqual(ScraperConfigurationStatus.RUNNING_SCRAPE);
            expect(result2.status).toEqual(ScraperConfigurationStatus.SCHEDULED);
        });
    });

    it('convert scraperConfiguration of source whish run is not running or being scheduled', async () => {
        const expectedNoRunScraperConfigurationMock = {source: Source.PLAYSTATION_SALES, status: ScraperConfigurationStatus.CONFIGURED} as ScraperConfiguration;
        const result3 = convertToPublicScraperConfiguration(expectedNoRunScraperConfigurationMock, createRunManager(1));
        expect(result3.status).toEqual(ScraperConfigurationStatus.CONFIGURED);
    });
});
