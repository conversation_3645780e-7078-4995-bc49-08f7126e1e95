import {BinaryProxy, ScrapeParams} from '../../../dependencies/BinaryProxy';
import {ScrapeResult} from '../../../processes/types';
import {Job} from '../../../runs/jobs/Job';
import {ScrapeJob} from '../../../runs/jobs/ScrapeJob';
import PriorityQueue from '../../../runs/PriorityQueue';
import {Run} from '../../../runs/Run';
import {RunManager} from '../../../runs/RunManager';
import {ScrapeRun, ScrapeRunContext} from '../../../runs/ScrapeRun';
import {TriggeredBy} from '../../../telemetry/scraperService/scraperServiceEvents';
import {Command, Source} from '../../../types';
import {sumDaysInDateRanges} from '../../../utils/datesUtils';
import {Deferred} from '../../../utils/Deferred';
import {createDateRange} from '../../utils/datesHelpers';
import {shadowModeScrapeTestTask} from '../../utils/entries';
import {TestRun, createRun, getJobAndItsDeferredPromise, testWithRunManagerContext} from '../../utils/RunUtils';
import {TestJob} from '../../utils/TestJob';

describe('RunManager', () => {
    afterEach(() => {
        jest.resetAllMocks();
    });

    test('should start job execution', async () => {
        await testWithRunManagerContext(async (runManagerInTest) => {
            const {job} = getJobAndItsDeferredPromise();

            const run = createRun([job]);
            await runManagerInTest.addRun(run);

            const runningRuns = runManagerInTest.getRunningRuns();
            expect(runningRuns.length).toBe(1);
            runningRuns.every((run) => expect(run.isActive()).toBe(true));
        });
    });

    describe('killing', () => {
        it('killAll should call kill() on all runs (running & scheduled)', async () => {
            await testWithRunManagerContext(async (runManagerInTest) => {
                const killSpy = jest.fn().mockResolvedValue(undefined);

                const sources = [Source.PLAYSTATION_SALES, Source.NINTENDO_SALES, Source.GOG_SALES, Source.STEAM_SALES, Source.HUMBLE_SALES];

                for (const source of sources) {
                    const {job} = getJobAndItsDeferredPromise();
                    const run = createRun([job], {source});
                    run.kill = killSpy;
                    await runManagerInTest.addRun(run);
                }
                await runManagerInTest.killAll();

                expect(killSpy).toHaveBeenCalledTimes(sources.length);
            });
        });

        it('killAll should kill all runs.', async () => {
            await testWithRunManagerContext(async (runManagerInTest) => {
                const {job} = getJobAndItsDeferredPromise();

                const run = createRun([job]);
                await runManagerInTest.addRun(run);

                expect(runManagerInTest.getRunningRuns().length).toBe(1);

                await runManagerInTest.killAll();
                expect(runManagerInTest.getRunningRuns().length).toBe(0);
            });
        });
    });

    describe('progress', () => {
        test('scrapeProgress equals to NaN if no runs are in the queue', async () => {
            await testWithRunManagerContext(async (runManagerInTest) => {
                expect(runManagerInTest.scrapeProgress).toBe(NaN);
            });
        });

        class ProgressTestRun extends Run {
            constructor(jobs: TestJob[], private _processedItems: number, private _totalItems: number, source: Source) {
                super(jobs, {
                    context: {
                        source,
                        command: Command.SCRAPE,
                        triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
                        forceRunStart: false
                    }
                });
            }

            get processedItems(): number {
                return this.isDone ? this.totalItems : this._processedItems;
            }

            get totalItems(): number {
                return this._totalItems;
            }
        }

        test('scrapeProgress equals to NaN if no scraping runs are in the queue', async () => {
            await testWithRunManagerContext(async (runManagerInTest) => {
                const deferred = new Deferred();
                const job = new TestJob(async () => {
                    await deferred.promise;
                });

                const run = createRun([job]);
                const promise = runManagerInTest.addRun(run);

                expect(runManagerInTest.getRunningRuns().length).toBe(1);
                expect(runManagerInTest.scrapeProgress).toBe(NaN);

                deferred.resolve();
                await promise;

                expect(runManagerInTest.scrapeProgress).toBe(NaN);
            });
        });

        test('getProgressOfRunsOfType equals 100 if all runs of specified type are finished, even if some of them failed', async () => {
            await testWithRunManagerContext(async (runManagerInTest) => {
                const jobAndDeferredTuple = getJobAndItsDeferredPromise();
                const jobAndDeferredTuple2 = getJobAndItsDeferredPromise();
                const jobAndDeferredTuple3 = getJobAndItsDeferredPromise();

                const run1 = createRun([jobAndDeferredTuple.job], {source: Source.PLAYSTATION_SALES});
                const run2 = createRun([jobAndDeferredTuple2.job], {source: Source.NINTENDO_SALES});
                const run3 = createRun([jobAndDeferredTuple3.job], {source: Source.STEAM_SALES});

                await runManagerInTest.addRun(run1);
                await runManagerInTest.addRun(run2);
                await runManagerInTest.addRun(run3);

                expect(runManagerInTest.getProgressOfRunsOfType(TestRun)).toBe(0);
                expect(runManagerInTest.getRunningRuns()).toHaveLength(1);
                expect(runManagerInTest.getScheduledRuns()).toHaveLength(2);

                jobAndDeferredTuple.deferred.resolve();
                await run1.getJobResults();
                expect(runManagerInTest.getProgressOfRunsOfType(TestRun)).toBe(33);

                /**
                 * https://stackoverflow.com/questions/67789309/why-do-i-get-an-unhandled-promise-rejection-with-await-promise-all
                 *
                 * No idea why this works in this order, but fails if we add listener sooner :O
                 * The link above was helpful, but why a similar test in priorityQueue without the listener works?
                 * da faq?
                 */
                jobAndDeferredTuple2.deferred.reject(new Error('Test error'));

                try {
                    await run2.getJobResults();
                } catch {
                    // ignore
                }

                expect(runManagerInTest.getProgressOfRunsOfType(TestRun)).toBe(67);
                expect(runManagerInTest.getRunningRuns()).toHaveLength(1);
                expect(runManagerInTest.getScheduledRuns()).toHaveLength(1);
                jobAndDeferredTuple3.deferred.resolve();
                await run3.getJobResults();

                const progressOfRunsOfType = runManagerInTest.getProgressOfRunsOfType(TestRun);
                expect(progressOfRunsOfType).toBe(100);
            });
        });

        test('getProgressOfRunsOfType calculates progress taking into account different size of Runs and adjusts when new runs are added', async () => {
            await testWithRunManagerContext(async (runManagerWithBiggerCapacity) => {
                const deferred = new Deferred();
                const job = new TestJob(async () => {
                    await deferred.promise;
                });

                await runManagerWithBiggerCapacity.addRun(new ProgressTestRun([job], 5, 20, Source.PLAYSTATION_SALES));
                expect(runManagerWithBiggerCapacity.getRunningRuns().length).toBe(1);

                expect(runManagerWithBiggerCapacity.getProgressOfRunsOfType(ProgressTestRun)).toBe((5 / 20) * 100);

                await runManagerWithBiggerCapacity.addRun(new ProgressTestRun([job], 7, 40, Source.APP_STORE_SALES));
                expect(runManagerWithBiggerCapacity.getRunningRuns().length).toBe(2);

                expect(runManagerWithBiggerCapacity.getProgressOfRunsOfType(ProgressTestRun)).toBe(((5 + 7) / (20 + 40)) * 100);

                const lastJobInQue = new ProgressTestRun([job], 41, 455, Source.STEAM_SALES);
                await runManagerWithBiggerCapacity.addRun(lastJobInQue);
                expect(runManagerWithBiggerCapacity.getRunningRuns().length).toBe(3);

                expect(runManagerWithBiggerCapacity.getProgressOfRunsOfType(ProgressTestRun)).toBe(Math.round(((5 + 7 + 41) / (20 + 40 + 455)) * 100));

                deferred.resolve();
                await lastJobInQue.getJobResults();

                expect(runManagerWithBiggerCapacity.getProgressOfRunsOfType(ProgressTestRun)).toBe(100);

                //----
                const runAfterQueueFinish = new ProgressTestRun([job], 5, 20, Source.GOG_SALES);

                await runManagerWithBiggerCapacity.addRun(runAfterQueueFinish);
                expect(runManagerWithBiggerCapacity.getRunningRuns().length).toBe(1);
                expect(runManagerWithBiggerCapacity.getScheduledRuns().length).toBe(0);
                expect(runManagerWithBiggerCapacity['runsForProgressCalculation'].length).toBe(1);
                expect(runManagerWithBiggerCapacity.getProgressOfRunsOfType(ProgressTestRun)).toBe((5 / 20) * 100);
            }, 3);
        });

        test('progress is reset once all active jobs finish and a new one is added', async () => {
            await testWithRunManagerContext(async (runManager) => {
                const deferred = new Deferred();
                const job = new TestJob(async () => {
                    await deferred.promise;
                });

                await runManager.addRun(new ProgressTestRun([job], 5, 20, Source.APP_STORE_SALES));

                await runManager.addRun(new ProgressTestRun([job], 7, 40, Source.GOG_SALES));

                const lastJobInQue = new ProgressTestRun([job], 41, 455, Source.META_QUEST_SALES);
                await runManager.addRun(lastJobInQue);

                deferred.resolve();
                await lastJobInQue.getJobResults();

                expect(runManager.getProgressOfRunsOfType(ProgressTestRun)).toBe(100);

                //----
                const runAfterQueueFinish = new ProgressTestRun([job], 5, 20, Source.PLAYSTATION_SALES);

                await runManager.addRun(runAfterQueueFinish);
                expect(runManager.getRunningRuns().length).toBe(1);
                expect(runManager.getScheduledRuns().length).toBe(0);
                expect(runManager['runsForProgressCalculation'].length).toBe(1); //TODO testing internals
                expect(runManager.getProgressOfRunsOfType(ProgressTestRun)).toBe((5 / 20) * 100);
            }, 3);
        });

        test('getProgressOfRunsOfType resets progress after new runs are added, only if progress earlier reached 100', async () => {
            await testWithRunManagerContext(async (runManager) => {
                const {job, deferred} = getJobAndItsDeferredPromise();

                const progressTestRun = new ProgressTestRun([job], 5, 20, Source.APP_STORE_SALES);
                await runManager.addRun(progressTestRun);
                expect(runManager.getRunningRuns().length).toBe(1);

                expect(runManager.getProgressOfRunsOfType(ProgressTestRun)).toBe((5 / 20) * 100);

                deferred.resolve();
                await progressTestRun.getJobResults();

                expect(runManager.getProgressOfRunsOfType(ProgressTestRun)).toBe(100);

                const getJobAndItsDeferredPromise2 = getJobAndItsDeferredPromise();

                const progressTestRun1 = new ProgressTestRun([getJobAndItsDeferredPromise2.job], 65, 547, Source.PLAYSTATION_SALES);
                await runManager.addRun(progressTestRun1);
                expect(runManager.getRunningRuns()).toHaveLength(1);

                expect(runManager.getProgressOfRunsOfType(ProgressTestRun)).toBe(Math.round((65 / 547) * 100));

                getJobAndItsDeferredPromise2.deferred.resolve();
                await progressTestRun1.getJobResults();

                expect(runManager.getProgressOfRunsOfType(ProgressTestRun)).toBe(100);
            }, 3);
        });

        describe('scrapeProgress events', () => {
            class MockScrapeRun extends Run<ScrapeRunContext> {
                constructor(jobs: Job[], context: ScrapeRunContext) {
                    super(jobs, {context});
                    // Change the prototype to make it an instance of ScrapeRun
                    Object.setPrototypeOf(this, ScrapeRun.prototype);
                }
            }

            class TestScrapeJob extends ScrapeJob {
                deferred: Deferred<void>;

                constructor(protected name: string) {
                    super({} as BinaryProxy, {} as ScrapeParams, jest.fn(), jest.fn());
                    this.deferred = new Deferred();
                }

                async execute(_context: ScrapeRunContext): Promise<ScrapeResult[]> {
                    await this.deferred.promise;
                    return [];
                }

                async onSuccess({dateRangesToProcess}: ScrapeRunContext): Promise<void> {
                    dateRangesToProcess.shift();
                }

                async finish(): Promise<void> {
                    this.deferred.resolve();
                    await new Promise((resolve) => setTimeout(resolve, 10));
                }
            }

            const dateRangeToProcess1a = createDateRange('2023-01-01', '2023-01-05');
            const dateRangeToProcess1b = createDateRange('2023-02-11', '2023-02-18');

            const dateRangesToProcess1 = [dateRangeToProcess1a, dateRangeToProcess1b];

            const job_1a = new TestScrapeJob('job_1a');
            const job_1b = new TestScrapeJob('job_1b');

            const scrapeRun1 = new MockScrapeRun([job_1a, job_1b], {
                dateRangesToProcess: dateRangesToProcess1,
                numberOfDaysToScrape: sumDaysInDateRanges(dateRangesToProcess1),
                source: Source.PLAYSTATION_SALES,
                command: Command.SCRAPE,
                triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
                isShadowRun: false,
                forceRunStart: false,
                operationId: 'fake'
                // sessionPath: 'fake',
                // mutable: {}
            });

            const dateRangeToProcess2a = createDateRange('2023-03-01', '2023-03-15');
            const dateRangeToProcess2b = createDateRange('2023-04-01', '2023-04-29');

            const dateRangesToProcess2 = [dateRangeToProcess2a, dateRangeToProcess2b];

            const job_2a = new TestScrapeJob('job_2a');
            const job_2b = new TestScrapeJob('job_2b');

            const scrapeRun2 = new MockScrapeRun([job_2a, job_2b], {
                dateRangesToProcess: dateRangesToProcess2,
                numberOfDaysToScrape: sumDaysInDateRanges(dateRangesToProcess2),
                source: Source.STEAM_WISHLISTS,
                command: Command.SCRAPE,
                triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
                isShadowRun: false,
                forceRunStart: false,
                operationId: 'fake'
                // sessionPath: 'fake',
                // mutable: {}
            });

            const shadowDateRangesToProcess = [createDateRange('2023-05-01', '2023-05-11'), createDateRange('2023-06-01', '2023-06-21')];

            const shadow_job_2a = new TestScrapeJob('shadow_job_2a');
            const shadow_job_2b = new TestScrapeJob('shadow_job_2b');

            const shadowRun = new MockScrapeRun([shadow_job_2a, shadow_job_2b], {
                dateRangesToProcess: dateRangesToProcess2,
                numberOfDaysToScrape: sumDaysInDateRanges(shadowDateRangesToProcess),
                source: Source.HUMBLE_SALES,
                command: Command.SCRAPE,
                triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
                shadowModeTask: shadowModeScrapeTestTask,
                isShadowRun: true,
                forceRunStart: false,
                operationId: 'fake'
                // sessionPath: 'fake',
                // mutable: {}
            });

            test('onScrapeProgress', async () => {
                const onScrapeProgress = jest.fn();
                const pq = new PriorityQueue(3);
                const runManager = new RunManager(onScrapeProgress, pq);

                const days1a = dateRangeToProcess1a.daysInRange;
                const days1b = dateRangeToProcess1b.daysInRange;
                const days2a = dateRangeToProcess2a.daysInRange;
                const days2b = dateRangeToProcess2b.daysInRange;

                await runManager.addRun(scrapeRun1);
                expect(onScrapeProgress).toHaveBeenNthCalledWith(1, 0);

                await job_1a.finish();
                expect(onScrapeProgress).toHaveBeenNthCalledWith(2, Math.round((days1a / (days1a + days1b)) * 100));

                await runManager.addRun(scrapeRun2);
                const OneOutOfFourUnevenDateRangesProgress = Math.round((days1a / (days1a + days1b + days2a + days2b)) * 100);
                expect(onScrapeProgress).toHaveBeenNthCalledWith(3, OneOutOfFourUnevenDateRangesProgress);

                await job_2a.finish();
                expect(onScrapeProgress).toHaveBeenNthCalledWith(4, Math.round(((days1a + days2a) / (days1a + days1b + days2a + days2b)) * 100));

                const shadowRunPromise = runManager.addRun(shadowRun); // should not trigger another event
                expect(onScrapeProgress).toHaveBeenCalledTimes(4);
                await shadow_job_2a.finish(); // should not trigger another event
                expect(onScrapeProgress).toHaveBeenCalledTimes(4);

                await job_2b.finish();
                expect(onScrapeProgress).toHaveBeenNthCalledWith(5, Math.round(((days1a + days2a + days2b) / (days1a + days1b + days2a + days2b)) * 100));

                await shadow_job_2b.finish(); // should not trigger another event
                expect(onScrapeProgress).toHaveBeenCalledTimes(5);

                await job_1b.finish();
                expect(onScrapeProgress).toHaveBeenNthCalledWith(6, Math.round(((days1a + days1b + days2a + days2b) / (days1a + days1b + days2a + days2b)) * 100));

                await shadowRunPromise;
                await scrapeRun1.getJobResults();
                await scrapeRun2.getJobResults();
            });
        });
    });

    describe('shadow mode', () => {
        function isShadowRunInProgress(runManager: RunManager) {
            return runManager.getRunningRunForSource(Source.PLAYSTATION_SALES, true) !== undefined;
        }

        test('a RunManager can executes a shadow run successfully', async () => {
            await testWithRunManagerContext(async (runManagerInTest) => {
                const {job, deferred} = getJobAndItsDeferredPromise();
                const shadowRun = createRun([job], {isShadowRun: true});
                await runManagerInTest.addRun(shadowRun);

                expect(isShadowRunInProgress(runManagerInTest)).toBeTruthy();

                deferred.resolve();
                await shadowRun.getJobResults();

                expect(isShadowRunInProgress(runManagerInTest)).toBeFalsy();
            });
        });

        test('ShadowRun executes even if there is a scrape run with the same source in progress', async () => {
            await testWithRunManagerContext(async (runManagerInTest) => {
                const {job} = getJobAndItsDeferredPromise();

                const normalRun = createRun([job]);
                await runManagerInTest.addRun(normalRun);
                const runningRuns = runManagerInTest.getRunningRuns();

                expect(runningRuns.length).toBe(1);
                runningRuns.every((run) => expect(run.isActive()).toBe(true));

                const {job: shadowJob, deferred} = getJobAndItsDeferredPromise();
                const shadowRun = createRun([shadowJob], {isShadowRun: true});
                await runManagerInTest.addRun(shadowRun);
                expect(isShadowRunInProgress(runManagerInTest)).toBeTruthy();

                deferred.resolve();
                await shadowRun.getJobResults();
                expect(isShadowRunInProgress(runManagerInTest)).toBeFalsy();
            }, 2);
        });

        test('ShadowRun waits for other runs to finish if limit is set to low', async () => {
            await testWithRunManagerContext(
                async (runManager) => {
                    const {job, deferred} = getJobAndItsDeferredPromise();

                    const normalRun = createRun([job]);
                    await runManager.addRun(normalRun);

                    const {job: shadowJob, deferred: shadowDeferred} = getJobAndItsDeferredPromise();
                    const shadowRun = createRun([shadowJob], {isShadowRun: true});
                    await runManager.addRun(shadowRun);

                    const normalRun_1 = runManager.getRunningRunForSource(Source.PLAYSTATION_SALES);
                    expect(normalRun_1).toBeDefined();

                    expect(isShadowRunInProgress(runManager)).toBeFalsy();

                    deferred.resolve();
                    await normalRun.getJobResults();
                    await sleep(1);

                    expect(isShadowRunInProgress(runManager)).toBeTruthy();

                    shadowDeferred.resolve();
                    await shadowRun.getJobResults();
                    expect(isShadowRunInProgress(runManager)).toBeFalsy();
                },
                1,
                1
            );
        });
    });
});

async function sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
