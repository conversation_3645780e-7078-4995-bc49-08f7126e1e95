import * as reportUpload from '../../../api/reportUpload';
import {UploadDTO} from '../../../api/types';
import {errorType} from '../../../configurations/errorType';
import {ScraperConfigurationStatus} from '../../../configurations/ScraperConfiguration';
import {BinaryProxy, CheckSessionParams, LoginParams, ScrapeParams} from '../../../dependencies/BinaryProxy';
import {DependenciesManager} from '../../../dependencies/DependenciesManager';
import {ScrapeResult} from '../../../processes/types';
import {ScraperLibError} from '../../../processes/types/errors';
import {CheckSessionJob, CheckSessionJobContext} from '../../../runs/jobs/CheckSessionJob';
import {DeleteReportJob} from '../../../runs/jobs/DeleteReportJob';
import {LoginJob} from '../../../runs/jobs/LoginJob';
import {ScrapeJob} from '../../../runs/jobs/ScrapeJob';
import {SyncDependenciesJob} from '../../../runs/jobs/SyncDependenciesJob';
import {UploadReportJob} from '../../../runs/jobs/UploadReportJob';
import {ScrapeRun, ScrapeRunContext} from '../../../runs/ScrapeRun';
import {TriggeredBy} from '../../../telemetry/scraperService/scraperServiceEvents';
import {DateRange, Source} from '../../../types';
import * as fileUtils from '../../../utils/fileUtils';
import {createDateRange} from '../../utils/datesHelpers';
import {shadowModeScrapeTestTask} from '../../utils/entries';
import {mockScraperServiceClient} from '../../utils/helpers';
import {getJobAndItsDeferredPromise} from '../../utils/RunUtils';

const sessionPath = 'fakePath';
const checkSessionResult = {id: 'test'};
let sendScraperStateChangedEventSpy: jest.SpyInstance;

function getScraperRunMocks(dateRanges: DateRange[] = [], context: Partial<ScrapeRunContext> = {}) {
    const updateStatusSpy = jest.fn();

    const run = new ScrapeRun(
        {
            mainDir: '',
            binaryProxy: {} as BinaryProxy,
            dateRanges: dateRanges,
            loginParams: {sessionPath: sessionPath} as LoginParams,
            checkSessionParams: {} as CheckSessionParams,
            dependenciesManager: {} as DependenciesManager,
            scrapeParams: {} as ScrapeParams,
            updateLastScrapeDate: jest.fn(),
            updateStatus: updateStatusSpy,
            s2Client: mockScraperServiceClient
        },
        {
            context: {
                ...(context as ScrapeRunContext)
            }
        }
    );
    return {updateStatusSpy, run};
}

const checkSessionExecuteMock = (context: CheckSessionJobContext) => {
    context.sessionValid = true;
    context.checkSessionResult = checkSessionResult;
    return Promise.resolve(checkSessionResult);
};

describe('ScrapeRun', () => {
    beforeEach(async () => {
        sendScraperStateChangedEventSpy = jest.spyOn(mockScraperServiceClient, 'scheduleScraperStateChangedEvent');
    });
    afterEach(async () => {
        jest.clearAllMocks();
        jest.restoreAllMocks();
    });

    const testDateRange = createDateRange('2020-01-01', '2020-01-07');

    it('should update status on job start', async () => {
        //Given
        const {updateStatusSpy, run} = getScraperRunMocks();
        jest.spyOn(SyncDependenciesJob.prototype, 'execute').mockResolvedValue();
        jest.spyOn(CheckSessionJob.prototype, 'execute').mockImplementation(checkSessionExecuteMock);

        // binaryProxyMock.run.mockResolvedValue({
        //     result: Promise.resolve(checkSessionResult)
        // });
        // binaryProxyMock.run.mockImplementation((cmd) => {
        //     if (cmd === Command.CHECK_SESSION) {
        //         return Promise.resolve({
        //             result: Promise.resolve(checkSessionResult)
        //         });
        //     }
        //     return Promise.resolve({
        //         result: Promise.resolve()
        //     });
        // });

        //When
        await run.executeAllJobs();
        //Then
        expect(updateStatusSpy).toBeCalledWith(ScraperConfigurationStatus.RUNNING_SCRAPE);
    });

    it('should update status on job success and sends STARTED and FINISHED events to S2', async () => {
        //Given
        const {updateStatusSpy, run} = getScraperRunMocks([], {source: Source.STEAM_SALES, triggeredBy: TriggeredBy.SCHEDULE});
        jest.spyOn(SyncDependenciesJob.prototype, 'execute').mockResolvedValue();
        jest.spyOn(CheckSessionJob.prototype, 'execute').mockImplementation(checkSessionExecuteMock);
        //When
        await run.executeAllJobs();
        //Then
        expect(updateStatusSpy.mock.calls).toHaveLength(2);
        expect(updateStatusSpy.mock.calls[1]).toEqual([ScraperConfigurationStatus.CONFIGURED]);

        expect(sendScraperStateChangedEventSpy).toBeCalledTimes(2);
        expect(sendScraperStateChangedEventSpy).nthCalledWith(1, {
            source: Source.STEAM_SALES,
            newState: 'STARTED',
            operationId: expect.anything(),
            triggeredBy: TriggeredBy.SCHEDULE,
            dateRanges: []
        });
        expect(sendScraperStateChangedEventSpy).nthCalledWith(2, {
            source: Source.STEAM_SALES,
            newState: 'FINISHED',
            operationId: expect.anything(),
            triggeredBy: TriggeredBy.SCHEDULE
        });
    });

    it('should update status on job failure and sends FAILED event to S2', async () => {
        const {updateStatusSpy, run} = getScraperRunMocks([], {source: Source.STEAM_SALES, triggeredBy: TriggeredBy.SCHEDULE});
        const error = new ScraperLibError(errorType.DEPENDENCIES_SYNC_ERROR);
        jest.spyOn(SyncDependenciesJob.prototype, 'execute').mockRejectedValue(error);

        await expect(run.executeAllJobs()).rejects.toThrow(error);

        expect(updateStatusSpy.mock.calls[1]).toEqual([ScraperConfigurationStatus.ERROR, {errorType: error.errorType}]);

        expect(sendScraperStateChangedEventSpy).toBeCalledTimes(2);
        expect(sendScraperStateChangedEventSpy).nthCalledWith(1, {
            source: Source.STEAM_SALES,
            newState: 'STARTED',
            operationId: expect.anything(),
            triggeredBy: TriggeredBy.SCHEDULE,
            dateRanges: []
        });
        expect(sendScraperStateChangedEventSpy).nthCalledWith(2, {
            source: Source.STEAM_SALES,
            newState: 'FAILED',
            operationId: expect.anything(),
            triggeredBy: TriggeredBy.SCHEDULE,
            accountIdentifier: undefined,
            reason: errorType.DEPENDENCIES_SYNC_ERROR
        });
    });

    it('should update status with additional error data when they are available', async () => {
        const {updateStatusSpy, run} = getScraperRunMocks();
        const additionalErrorData = {someKindOfError: 'data'};
        const error = new ScraperLibError(errorType.INCORRECT_CREDENTIALS, 'error', additionalErrorData);
        jest.spyOn(SyncDependenciesJob.prototype, 'execute').mockRejectedValue(error);

        await expect(run.executeAllJobs()).rejects.toThrow(error);

        expect(updateStatusSpy.mock.calls[1]).toEqual([ScraperConfigurationStatus.ERROR, {errorType: error.errorType, additionalErrorData}]);
    });

    /**
     * On scheduled is called externally only hence the different call pattern.
     */
    test('should update status to scheduled on onScheduled', async () => {
        const {updateStatusSpy, run} = getScraperRunMocks();

        await run['onScheduled']!();

        expect(updateStatusSpy).toBeCalledTimes(1);
        expect(updateStatusSpy).toBeCalledWith(ScraperConfigurationStatus.SCHEDULED);
    });

    test('should not run remove action for non shadow run', async () => {
        const {updateStatusSpy, run} = getScraperRunMocks();
        jest.spyOn(fileUtils, 'removeFileOrDirectory');

        await run['onFinally']!();

        expect(updateStatusSpy).toBeCalledTimes(0);
    });

    test('should run remove action for shadow run', async () => {
        const {run} = getScraperRunMocks([], {shadowModeTask: shadowModeScrapeTestTask});
        const removeFileOrDirectorySpy = jest.spyOn(fileUtils, 'removeFileOrDirectory');

        await run['onFinally']!();

        expect(removeFileOrDirectorySpy).toBeCalledTimes(1);
        expect(removeFileOrDirectorySpy).toBeCalledWith(sessionPath, false);
    });

    test('should have all needed jobs', async () => {
        const {run} = getScraperRunMocks([testDateRange, testDateRange]);
        expect(run.jobs.length).toBe(9);
        expect(run.jobs.filter((job) => job instanceof SyncDependenciesJob).length).toBe(1);
        expect(run.jobs.filter((job) => job instanceof CheckSessionJob).length).toBe(1);
        expect(run.jobs.filter((job) => job instanceof LoginJob).length).toBe(1);
        expect(run.jobs.filter((job) => job instanceof ScrapeJob).length).toBe(2);
        expect(run.jobs.filter((job) => job instanceof UploadReportJob).length).toBe(2);
        expect(run.jobs.filter((job) => job instanceof DeleteReportJob).length).toBe(2);
    });

    test('should return proper amount of items', async () => {
        const {run} = getScraperRunMocks([testDateRange], {
            numberOfDaysToScrape: testDateRange.daysInRange,
            dateRangesToProcess: [testDateRange]
        });

        expect(run.processedItems).toEqual(0);
    });

    test('should return proper amount of total items', async () => {
        const {run} = getScraperRunMocks([testDateRange], {numberOfDaysToScrape: testDateRange.daysInRange});

        expect(run.totalItems).toEqual(testDateRange.daysInRange);
    });

    test("shouldn't change status when killed and sends STOPPED event to S2", async () => {
        // Given
        const {run, updateStatusSpy} = getScraperRunMocks([], {source: Source.STEAM_SALES, triggeredBy: TriggeredBy.SCHEDULE});

        const {job, deferred} = getJobAndItsDeferredPromise('job1');
        run.jobs[0] = job;

        run.executeAllJobs().catch(console.error);
        const preKillUpdateAmount = 1;
        expect(updateStatusSpy).toBeCalledTimes(preKillUpdateAmount);
        expect(updateStatusSpy).toBeCalledWith(ScraperConfigurationStatus.RUNNING_SCRAPE);

        // we are making sure, that first job will start before we will kill whole run
        deferred.resolve();
        await deferred.promise;

        // When
        await run.kill();

        // Then
        expect(updateStatusSpy).toBeCalledTimes(preKillUpdateAmount);

        expect(sendScraperStateChangedEventSpy).toBeCalledTimes(2);
        expect(sendScraperStateChangedEventSpy).nthCalledWith(1, {
            source: Source.STEAM_SALES,
            newState: 'STARTED',
            operationId: expect.anything(),
            triggeredBy: TriggeredBy.SCHEDULE,
            dateRanges: []
        });
        expect(sendScraperStateChangedEventSpy).nthCalledWith(2, {
            source: Source.STEAM_SALES,
            newState: 'STOPPED',
            operationId: expect.anything(),
            triggeredBy: TriggeredBy.SCHEDULE
        });
    });

    test('should retry execution of UploadReportJob in case of any failures', async () => {
        const {run} = getScraperRunMocks([testDateRange], {latestScrapeResults: [{} as ScrapeResult]});
        jest.spyOn(SyncDependenciesJob.prototype, 'execute').mockResolvedValue();
        jest.spyOn(CheckSessionJob.prototype, 'execute').mockImplementation(checkSessionExecuteMock);
        jest.spyOn(ScrapeJob.prototype, 'onSuccess').mockResolvedValue();
        jest.spyOn(ScrapeJob.prototype, 'execute').mockImplementation();

        const uploadReportFnSpy = jest
            .spyOn(reportUpload, 'uploadReport')
            .mockRejectedValueOnce('Upload failed')
            .mockRejectedValueOnce('Upload failed')
            .mockResolvedValueOnce({} as any as UploadDTO);

        jest.spyOn(reportUpload, 'sendReportUploadInfo').mockResolvedValue({} as any);
        const deleteReportFnSpy = jest.spyOn(reportUpload, 'deleteReport').mockResolvedValue();

        await run.executeAllJobs();

        expect(uploadReportFnSpy).toHaveBeenCalledTimes(3);
        expect(deleteReportFnSpy).toHaveBeenCalledTimes(1);
    });
});
