import {errorType} from '../../../../configurations/errorType';
import {BinaryProxy, CheckSessionParams, CommonParams} from '../../../../dependencies/BinaryProxy';
import {ScraperLibError} from '../../../../processes/types/errors';
import {CheckSessionJob, CheckSessionJobContext} from '../../../../runs/jobs/CheckSessionJob';
import {Command} from '../../../../types';

describe('CheckSessionJob', () => {
    afterEach(jest.restoreAllMocks);

    test('execute checks the session and ends gracefully if it is ok', async () => {
        const binaryProxyMock = {
            run: jest.fn().mockResolvedValue({
                result: Promise.resolve({hasScrapeBlockingIssues: false, id: 'test'})
            })
        } as unknown as BinaryProxy;
        const job = new CheckSessionJob(binaryProxyMock, {} as CheckSessionParams);
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        await job.execute(context);
        expect(context.sessionValid).toBe(true);
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, {} as CommonParams, context.operationId);
    });

    test('execute checks the session but is killed before it ends', async () => {
        const binaryProxyMock = {
            run: jest.fn().mockResolvedValue({
                result: Promise.reject()
            })
        } as unknown as BinaryProxy;
        const job = new CheckSessionJob(binaryProxyMock, {} as CheckSessionParams);
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        await job.kill(context);
        expect(context.sessionValid).toBe(undefined);
        await expect(job.execute(context)).rejects.toThrow(new ScraperLibError(errorType.JOB_STOPPED_BY_USER));
    });

    test('execute sets sessionValid to false in case of invalid session', async () => {
        const binaryProxyMock = {
            run: jest.fn().mockResolvedValue({
                result: Promise.reject({hasScrapeBlockingIssues: true, id: 'test'})
            })
        } as unknown as BinaryProxy;

        const checkSessionParams = {} as CheckSessionParams;
        const job = new CheckSessionJob(binaryProxyMock, checkSessionParams);
        const context = {operationId: 'fake'} as CheckSessionJobContext;
        await job.execute(context);
        expect(context.sessionValid).toBe(false);
        expect(binaryProxyMock.run).toHaveBeenCalledWith(Command.CHECK_SESSION, checkSessionParams, context.operationId);
    });
});
