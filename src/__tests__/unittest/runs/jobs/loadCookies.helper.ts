import {ScraperConfigurationManager} from '../../../../configurations/ScraperConfigurationManager';
import {BinaryProxy} from '../../../../dependencies/BinaryProxy';
import {DependenciesManager} from '../../../../dependencies/DependenciesManager';
import {LoginWithCookiesRun, LoginWithCookiesRunContext} from '../../../../runs/LoginWithCookiesRun';
import {TriggeredBy} from '../../../../telemetry/scraperService/scraperServiceEvents';
import {Source} from '../../../../types';
import * as fileUtils from '../../../../utils/fileUtils';
import {mockScraperServiceClient} from '../../../utils/helpers';

const createPartialMockContext = (overrides: Partial<LoginWithCookiesRunContext> = {}): Partial<LoginWithCookiesRunContext> => ({
    sessionPath: 'fake/session/path',
    cookies: ['cookie1', 'cookie2'],
    source: Source.STEAM_SALES,
    triggeredBy: TriggeredBy.USER_VIA_ELECTRON,
    ...overrides
});

export const createLoginWithCookiesRun = (context: Partial<LoginWithCookiesRunContext> = {}, binaryProxy: BinaryProxy = {} as BinaryProxy) => {
    return new LoginWithCookiesRun(
        {
            dependenciesManager: {} as DependenciesManager,
            binaryProxy,
            s2Client: mockScraperServiceClient,
            scraperConfigurationManager: jest.fn() as unknown as ScraperConfigurationManager
        },
        {
            context: createPartialMockContext(context) as LoginWithCookiesRunContext
        }
    );
};

export function getRemoveFileOrDirectorySpy() {
    return jest.spyOn(fileUtils, 'removeFileOrDirectory').mockImplementation(async (_path, throwOnFail) => {
        // Simulate the real behavior: when throwOnFail=false, errors are caught and not thrown
        if (throwOnFail === false) {
            return Promise.resolve(); // Don't throw, just resolve
        }
        throw new Error('File removal failed');
    });
}

export const createMockLoginWithCookiesRunContext = (overrides: Partial<LoginWithCookiesRunContext> = {}): LoginWithCookiesRunContext => ({
    sessionPath: 'fake/session/path',
    cookies: ['cookie1', 'cookie2'],
    source: 'STEAM_SALES' as any,
    command: 'LOGIN' as any,
    isShadowRun: false,
    forceRunStart: false,
    operationId: 'test-operation-id',
    triggeredBy: 'USER_VIA_ELECTRON' as any,

    // checkSessionResult: undefined,

    ...overrides
});
