export type SourceAccountEditParams = Partial<SourceAccount> & Pick<SourceAccount, 'id' | 'accountIdentifier'>;
export type SourceAccountAddParams = Partial<Omit<SourceAccount, 'id'>> & Pick<SourceAccount, 'accountIdentifier' | 'sessionPath'>;

/**
 Used to store all login data to specific portal, including a cookie/authToken/other if the login was successful.
 */
export interface SourceAccount {
    readonly id: string;

    /**
     * Identifier unique for source account.
     * This identifier is always returned by `login` and `check-session` command
     */
    readonly accountIdentifier: string;
    /**
     * Depending on the source it is used for this can be a cookie, set of cookies or an API authentication token.
     *
     * NOTE: a present session does not have to mean a valid session! Sessions change, are invalidated and timeout all the time.
     */
    readonly sessionPath: string;

    // TODO: probably not needes, as it is not even used.
    // /**
    //  * Required in the Microsoft API scraper where we need to know about all the user organizations in order to set them up
    //  */
    // allSourceSideOrganizations?: string[];

    /**
     * source/portal specific properties required by binaries
     */
    cliParams?: Record<string, any>;
}
