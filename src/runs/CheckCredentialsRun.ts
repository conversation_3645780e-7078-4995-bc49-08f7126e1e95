import {ScraperConfigurationManager} from '../configurations/ScraperConfigurationManager';
import {BinaryProxy, LoginParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {BinaryLoginResult} from '../processes/types/resultTypes';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import {DeleteSessionJob, DeleteSessionJobContext} from './jobs/DeleteSessionJob';
import {LoginJob, LoginJobContext} from './jobs/LoginJob';
import {SyncDependenciesJob, SyncDependenciesJobContext} from './jobs/SyncDependenciesJob';
import {Run} from './Run';

export type CheckCredentialsRunContext = SyncDependenciesJobContext & LoginJobContext & DeleteSessionJobContext;

export class CheckCredentialsRun extends Run<CheckCredentialsRunContext, BinaryLoginResult> {
    constructor(
        dependencies: {
            dependenciesManager: DependenciesManager;
            binaryProxy: BinaryProxy;
            loginParams: LoginParams;
            s2Client: iScraperServiceClient;
            scraperConfigurationManager: ScraperConfigurationManager;
        },
        options: {
            context?: Omit<CheckCredentialsRunContext, 'operationId' | 'isShadowRun'>;
            onScheduled?: () => Promise<void>;
            onStart?: () => Promise<void>;
            onSuccess?: () => Promise<void>;
        } = {}
    ) {
        super(
            [
                new SyncDependenciesJob(dependencies.dependenciesManager),
                // Login & Create Session
                new LoginJob(dependencies.binaryProxy, dependencies.loginParams),
                // Delete Session
                new DeleteSessionJob()
            ],
            {
                ...options
            }
        );
    }
}
