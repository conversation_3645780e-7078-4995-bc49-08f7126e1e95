import {errorType} from '../configurations/errorType';
import {ScraperConfigurationStatus} from '../configurations/ScraperConfiguration';
import {BinaryProxy, CheckSessionParams, LoginParams, ScrapeParams} from '../dependencies/BinaryProxy';
import {DependenciesManager} from '../dependencies/DependenciesManager';
import {iScraperServiceClient} from '../telemetry/scraperService/iScraperServiceClient';
import {DateRange, Source} from '../types';
import {sumDaysInDateRanges} from '../utils/datesUtils';
import {removeFileOrDirectory} from '../utils/fileUtils';
import {CheckSessionJob, CheckSessionJobContext} from './jobs/CheckSessionJob';
import {DeleteReportJob, DeleteReportJobContext} from './jobs/DeleteReportJob';
import {EnsureValidSessionJob, EnsureValidSessionJobContext} from './jobs/EnsureValidSessionJob';
import {LoginJob} from './jobs/LoginJob';
import {ScrapeJob, ScrapeJobContext} from './jobs/ScrapeJob';
import {SyncDependenciesJob, SyncDependenciesJobContext} from './jobs/SyncDependenciesJob';
import {UploadReportJob, UploadReportJobContext} from './jobs/UploadReportJob';
import {Run} from './Run';

export type ScrapeRunContext = SyncDependenciesJobContext &
    CheckSessionJobContext &
    EnsureValidSessionJobContext &
    ScrapeJobContext &
    UploadReportJobContext &
    DeleteReportJobContext;

export class ScrapeRun extends Run<ScrapeRunContext> {
    dateRanges: DateRange[];
    constructor(
        dependencies: {
            mainDir: string;
            dependenciesManager: DependenciesManager;
            binaryProxy: BinaryProxy;
            checkSessionParams: CheckSessionParams;
            loginParams: LoginParams;
            scrapeParams: Omit<ScrapeParams, 'dateFrom' | 'dateTo'>;
            updateStatus: (status: ScraperConfigurationStatus, additionalParams?: any) => Promise<void>;
            updateLastScrapeDate: (source: Source) => Promise<void>;
            dateRanges: DateRange[];
            s2Client: iScraperServiceClient;
        },
        options: {
            priority?: number;
            context?: Omit<ScrapeRunContext, 'operationId' | 'isShadowRun'>;
            onScheduled?: () => Promise<void>;
            onStart?: () => Promise<void>;
            onSuccess?: () => Promise<void>;
        } = {}
    ) {
        super(
            [
                new SyncDependenciesJob(dependencies.dependenciesManager),
                new CheckSessionJob(dependencies.binaryProxy, dependencies.checkSessionParams),
                new LoginJob(dependencies.binaryProxy, dependencies.loginParams),
                // new EnsureValidSessionJob(dependencies.binaryProxy, dependencies.checkSessionParams, dependencies.loginParams),
                ...dependencies.dateRanges.flatMap((dateRange) => [
                    new ScrapeJob(dependencies.binaryProxy, {...dependencies.scrapeParams, ...dateRange}, dependencies.updateStatus, dependencies.updateLastScrapeDate),
                    new UploadReportJob(dependencies.mainDir, dependencies.s2Client),
                    new DeleteReportJob(dependencies.mainDir)
                ])
            ],
            {
                ...options,
                onScheduled: async () => {
                    await dependencies.updateStatus(ScraperConfigurationStatus.SCHEDULED);
                    dependencies.s2Client.scheduleScraperStateChangedEvent({
                        source: this.context.source,
                        newState: 'SCHEDULED',
                        operationId: this.id,
                        triggeredBy: this.context.triggeredBy,
                        dateRanges: this.dateRanges
                    });
                },
                onStart: async () => {
                    await dependencies.updateStatus(ScraperConfigurationStatus.RUNNING_SCRAPE);
                    dependencies.s2Client.scheduleScraperStateChangedEvent({
                        source: this.context.source,
                        newState: 'STARTED',
                        operationId: this.id,
                        triggeredBy: this.context.triggeredBy,
                        dateRanges: this.dateRanges
                    });
                },
                onSuccess: async () => {
                    await dependencies.updateStatus(ScraperConfigurationStatus.CONFIGURED);
                    dependencies.s2Client.scheduleScraperStateChangedEvent({
                        source: this.context.source,
                        newState: 'FINISHED',
                        operationId: this.id,
                        triggeredBy: this.context.triggeredBy
                    });
                },
                onFail: async (e: any) => {
                    await dependencies.updateStatus(ScraperConfigurationStatus.ERROR, {errorType: e?.errorType, additionalErrorData: e?.additionalErrorData});
                    dependencies.s2Client.scheduleScraperStateChangedEvent({
                        source: this.context.source,
                        newState: 'FAILED',
                        operationId: this.id,
                        triggeredBy: this.context.triggeredBy,
                        accountIdentifier: undefined,
                        reason: e?.errorType ?? errorType.UNEXPECTED_ERROR
                    });
                },
                onFinally: async () => {
                    if (this.context.isShadowRun && dependencies.loginParams?.sessionPath) {
                        await removeFileOrDirectory(dependencies.loginParams.sessionPath, false);
                    }
                },
                onKill: async () => {
                    dependencies.s2Client.scheduleScraperStateChangedEvent({
                        source: this.context.source,
                        newState: 'STOPPED',
                        operationId: this.id,
                        triggeredBy: this.context.triggeredBy
                    });
                }
            }
        );
        /*
           TODO the original date ranges are modified by the jobs.
            We need to clone them since this information is required for the UI.
            It would be best if we would not modify them and keep this information non modified in the context.
        */
        this.dateRanges = dependencies.dateRanges.map((dateRange) => ({...dateRange}));
    }

    get processedItems(): number {
        return this.context.numberOfDaysToScrape - sumDaysInDateRanges(this.context.dateRangesToProcess);
    }

    get totalItems(): number {
        return this.context.numberOfDaysToScrape;
    }
}
