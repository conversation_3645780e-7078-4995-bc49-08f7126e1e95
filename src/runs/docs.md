|       Jobs \ Run              | LoginWithCookiesRun | LoginWithCredentialsRun | ScrapeRun | GetSourceSideOrganizationsRun | GetManualLoginDataRun |
|-------------------------------|:-------------------:|:-----------------------:|:---------:|:-----------------------------:|:---------------------:|
| SyncDependenciesJob           |         ✅          |           ✅            |     ✅    |                               |                       | 
| SaveCookiesToSessionJob       |         ✅          |                         |           |                               |                       |
| EnsureValidSessionJob         |         ✅          |                         |     ✅    |                               |                       |
| LoginJob                      |                     |           ✅            |           |                               |                       |
| CheckSessionJob               |                     |                         |           |                               |                       |
| SaveConfigurationJob          |         ✅          |                         |           |                               |                       |
| ScrapeJob                     |                     |                         |     ✅    |                               |                       |
| UploadReportJob               |                     |                         |     ✅    |                               |                       |
| DeleteReportJob               |                     |                         |     ✅    |                               |                       |
| GetSourceSideOrganizationsJob |                     |                         |           |               ✅              |                       |
| GetManualLoginDataJob         |                     |                         |           |                               |           ✅          |



- test, that login session is not run if checksession logged in
- sessionPath is always calculated based on shadowMode task and source