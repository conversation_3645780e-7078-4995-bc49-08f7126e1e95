import * as telemetry from '../../telemetry/telemetry';
import {removeFileOrDirectory} from '../../utils/fileUtils';
import {BaseContext} from './context';
import {Job} from './Job';

/* eslint-disable @typescript-eslint/no-empty-function */

export interface DeleteSessionJobContext extends BaseContext {
    readonly sessionPath: string;
}

export class DeleteSessionJob extends Job<void, DeleteSessionJobContext> {
    async execute({sessionPath}: DeleteSessionJobContext): Promise<void> {
        await removeFileOrDirectory(sessionPath);
    }

    async onSuccess(): Promise<void> {}

    async onFail(error: Error): Promise<void> {
        telemetry.trace('DeleteSessionJob failed');
        telemetry.exception(error, false, {operation: 'DeleteSessionJob'});
    }

    async kill(): Promise<void> {}
}
