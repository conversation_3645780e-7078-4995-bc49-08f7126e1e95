import {errorType} from '../../configurations/errorType';
import {BinaryProxy, CheckSessionParams, LoginParams} from '../../dependencies/BinaryProxy';
import {CheckSessionResult} from '../../processes/types';
import {ScraperLibError} from '../../processes/types/errors';
import {Execution} from '../../processes/types/Execution';
import * as telemetry from '../../telemetry/telemetry';
import {Command} from '../../types';
import {JobStoppedByUserError} from '../JobStoppedByUserError';
import {BaseContext} from './context';
import {Job} from './Job';

export interface EnsureValidSessionJobContext extends BaseContext {
    checkSessionResult?: CheckSessionResult;
}

export class EnsureValidSessionJob extends Job<CheckSessionResult, EnsureValidSessionJobContext> {
    private execution?: Execution<CheckSessionResult>;
    /**
     We need to track additionally if this job was killed because in case of errors (and killing a job can cause errors)
     It can trigger an additional binary run (which should not happen).
     Consider refactoring this mechanism so that the job spawn is done outside and this killed state is not required.
     */
    private isKilled = false;
    constructor(private binaryProxy: BinaryProxy, private checkSessionParams: CheckSessionParams, private loginParams: LoginParams) {
        super();
    }

    async execute(context: EnsureValidSessionJobContext): Promise<CheckSessionResult> {
        try {
            this.execution = await this.binaryProxy.run<CheckSessionResult>(Command.CHECK_SESSION, this.checkSessionParams, context.operationId);
            const result = await this.execution.result;

            if (result.hasScrapeBlockingIssues) {
                throw new ScraperLibError(errorType.MISSING_PERMISSIONS, 'error', {message: 'Session has scrape blocking issues', context, result});
            }

            telemetry.trace(`Session is valid`);
            context.checkSessionResult = result; // because we need this in the login runs we need to pollute this in scraper run context, and suddenly everything seems to be connected with everything and extends everything
            return result;
        } catch (e) {
            if (e && e.errorType === errorType.MISSING_PERMISSIONS) {
                throw e;
            }
            if (this.isKilled) {
                telemetry.trace('Job was killed');
                throw new JobStoppedByUserError('ENSURE_VALID_SESSION_EXECUTE_KILL');
            }
            if (this.loginParams.credentials) {
                telemetry.trace('Session is invalid, logging in because we have credentials');
                const params = {...this.loginParams};
                this.execution = await this.binaryProxy.run<CheckSessionResult>(Command.LOGIN, params, context.operationId);
                return await this.execution.result;
            } else {
                telemetry.trace('No credentials provided, skipping login');
                throw new ScraperLibError(errorType.SESSION_EXPIRED, 'error');
            }
        }
    }

    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(_error: Error, _context: any): Promise<void> {
        // Handle failure
    }

    async kill(context: EnsureValidSessionJobContext): Promise<void> {
        this.isKilled = true;
        await this.execution?.kill(context.operationId);
    }
}
