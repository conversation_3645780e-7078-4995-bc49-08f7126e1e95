import {ScraperConfiguration} from '../../configurations/ScraperConfiguration';
import {BinaryResult} from '../../processes/types';
import {RetryActionOptions} from '../../utils/retryAction';
import {BaseContext} from './context';

export type LoginResult = ScraperConfiguration[];

export type JobResult = BinaryResult | LoginResult | void;

export abstract class Job<Result extends JobResult = any, Context extends BaseContext = BaseContext> {
    dependencies: Job<Result>[] = [];
    retryOptions: Omit<RetryActionOptions<any>, 'target'> = {maxAttempts: 1};

    abstract execute(context: Context): Promise<Result>;

    abstract onFail(error: Error, context: Context): Promise<void>;

    abstract onSuccess(context: Context): Promise<void>;

    abstract kill(context: Context): Promise<void>;

    shouldRun(_context: Context, _previousResults: JobResult[]): boolean {
        return true;
    }
}
