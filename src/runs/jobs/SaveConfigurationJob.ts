import {errorType} from '../../configurations/errorType';
import {ScraperConfiguration} from '../../configurations/ScraperConfiguration';
import {ScraperConfigurationManager} from '../../configurations/ScraperConfigurationManager';
import {ScraperLibError} from '../../processes/types/errors';
import {CheckSessionResult} from '../../processes/types/resultTypes';
import {BaseContext} from './context';
import {Job} from './Job';

export interface SaveConfigurationJobContext extends BaseContext {
    readonly sessionPath: string;
    readonly checkSessionResult?: CheckSessionResult;
}

export class SaveConfigurationJob extends Job<ScraperConfiguration[], SaveConfigurationJobContext> {
    constructor(private readonly scraperConfigurationManager: ScraperConfigurationManager) {
        super();
    }

    async execute(context: SaveConfigurationJobContext): Promise<ScraperConfiguration[]> {
        if (!context.checkSessionResult || !context.checkSessionResult.id) {
            throw new ScraperLibError(errorType.INTERNAL_SCRAPER_LIB_ERROR, 'error', {
                message: 'CheckSessionResult is not defined and a configuration is trying to be saved',
                context
            });
        }

        return await this.scraperConfigurationManager.addSourceAccountAndHandleRelatedConfigs(context.source, {
            sessionPath: context.sessionPath,
            accountIdentifier: context.checkSessionResult!.id
        });
    }

    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(_error: Error): Promise<void> {
        // Handle failure
    }

    async kill(): Promise<void> {
        // Kill the job
    }
}
