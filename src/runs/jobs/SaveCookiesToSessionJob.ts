import {EncryptedJsonFile} from '../../utils/EncryptedJsonFile';
import {removeFileOrDirectory} from '../../utils/fileUtils';
import {BaseContext} from './context';
import {Job} from './Job';

export interface SaveCookiesToSessionJobContext extends BaseContext {
    readonly cookies: string[];
    readonly sessionPath: string;
}

export class SaveCookiesToSessionJob extends Job<void, SaveCookiesToSessionJobContext> {
    async execute({cookies, sessionPath}: SaveCookiesToSessionJobContext): Promise<void> {
        const encryptedJsonFile = new EncryptedJsonFile(sessionPath);
        await encryptedJsonFile.save({cookies});
    }

    async kill({sessionPath}: SaveCookiesToSessionJobContext): Promise<void> {
        return removeFileOrDirectory(sessionPath, false);
    }

    onFail(_error: Error, {sessionPath}: SaveCookiesToSessionJobContext): Promise<void> {
        return removeFileOrDirectory(sessionPath, false);
    }

    onSuccess(_context: SaveCookiesToSessionJobContext): Promise<void> {
        return Promise.resolve(undefined);
    }
}
