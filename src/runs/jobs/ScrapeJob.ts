import {ScraperConfigurationStatus} from '../../configurations/ScraperConfiguration';
import {BinaryProxy, ScrapeParams} from '../../dependencies/BinaryProxy';
import {ScrapeResult} from '../../processes/types';
import {Execution} from '../../processes/types/Execution';
import {Command, DateRange} from '../../types';
import {sumDaysInDateRanges} from '../../utils/datesUtils';
import {UpdateLastScrapeDate, UpdateStatus} from '../status';
import {BaseContext} from './context';
import {Job} from './Job';

export interface ScrapeJobContext extends BaseContext {
    readonly dateRangesToProcess: DateRange[];
    latestScrapeResults?: ScrapeResult[];
    numberOfDaysToScrape: number;
}

export class ScrapeJob extends Job<ScrapeResult[], ScrapeJobContext> {
    private execution?: Execution<ScrapeResult[]>;

    constructor(private binaryProxy: BinaryProxy, private params: ScrapeParams, private updateStatus: UpdateStatus, private updateLastScrapeDate: UpdateLastScrapeDate) {
        super();
    }

    async execute(context: ScrapeJobContext): Promise<ScrapeResult[]> {
        this.execution = await this.binaryProxy.run<ScrapeResult[]>(Command.SCRAPE, this.params, context.operationId);
        context.latestScrapeResults = await this.execution.result; //TODO context should not be modified inside jobs. Jobs should return their results
        return context.latestScrapeResults;
    }

    async onSuccess({dateRangesToProcess, numberOfDaysToScrape}: ScrapeJobContext): Promise<void> {
        dateRangesToProcess.pop();
        const scrapedDays = numberOfDaysToScrape - sumDaysInDateRanges(dateRangesToProcess);

        // TODO: numberOfDaysToScrape could be pre-calculated in RunFactory
        await this.updateStatus(ScraperConfigurationStatus.RUNNING_SCRAPE, {scrapedDays, numberOfDaysToScrape});
        await this.updateLastScrapeDate(this.params.source);
    }

    async onFail(_error: Error) {
        // Handle failure
    }

    async kill(context: ScrapeJobContext): Promise<void> {
        await this.execution?.kill(context.operationId);
    }
}
