import {DependenciesManager} from '../../dependencies/DependenciesManager';
import {BaseContext} from './context';
import {Job} from './Job';

export type SyncDependenciesJobContext = BaseContext;
export class SyncDependenciesJob extends Job<void> {
    constructor(private dependenciesManager: DependenciesManager) {
        super();
    }

    async execute(): Promise<void> {
        await this.dependenciesManager.syncDependencies(false, true);
    }

    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(): Promise<void> {
        // Handle failure
    }

    async kill(): Promise<void> {
        // Kill the job
    }
}
