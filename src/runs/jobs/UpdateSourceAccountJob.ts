import {ScraperConfigurationManager} from '../../configurations/ScraperConfigurationManager';
import {SourceAccount} from '../../configurations/SourceAccount';
import {LoginParams} from '../../dependencies/BinaryProxy';
import {BinaryLoginResult} from '../../processes/types/resultTypes';
import {BaseContext} from './context';
import {Job} from './Job';

export interface UpdateSourceAccountJobContext extends BaseContext {
    readonly sessionPath: string;
    readonly checkSessionResult?: BinaryLoginResult;
}

export class UpdateSourceAccountJob extends Job<SourceAccount, UpdateSourceAccountJobContext> {
    constructor(private readonly scraperConfigurationManager: ScraperConfigurationManager, private loginParams: LoginParams, private sourceAccountId: string) {
        super();
    }

    async execute({checkSessionResult, sessionPath}: UpdateSourceAccountJobContext): Promise<SourceAccount> {
        return await this.scraperConfigurationManager.editSourceAccount({
            id: this.sourceAccountId,
            cliParams: this.loginParams.credentials,
            accountIdentifier: checkSessionResult!.id,
            sessionPath
        });
    }

    async onSuccess(): Promise<void> {
        // Handle success
    }

    async onFail(_error: Error): Promise<void> {
        // Handle failure
    }

    async kill(): Promise<void> {
        // Kill the job
    }
}
