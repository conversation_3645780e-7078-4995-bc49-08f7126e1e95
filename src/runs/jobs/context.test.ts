/* eslint-disable @typescript-eslint/no-unused-vars */
import {Source} from '../../types';
import {AllKeys, BaseContext, ComputeRequired<PERSON>ields, ComputeRunContext, <PERSON><PERSON><PERSON>, IsR<PERSON>only, IsRequiredField} from './context';

// === TEST UTILITIES ===
type Expect<T extends true> = T;
type Equal<X, Y> = [X] extends [Y] ? ([Y] extends [X] ? true : false) : false;

// === TEST INTERFACES ===
interface TestReadonlyInterface {
    readonly readonlyField: string;
    mutableField: string;
    readonly optionalReadonly?: number;
    optionalMutable?: number;
}

interface JobA {
    readonly source: string;
    sessionPath: string;
}

interface JobB {
    readonly source: string;
    readonly sessionPath: string;
    loginResult: string;
}

interface JobC {
    readonly loginResult: string;
    configPath: string;
}

interface SyncJobContext {
    readonly source: string;
    tempDir: string;
}

interface LoginJobContext {
    readonly source: string;
    readonly tempDir: string;
    loginResult: BinaryLoginResult;
}

interface SaveJobContext {
    readonly source: string;
    readonly loginResult: BinaryLoginResult;
}

type BinaryLoginResult = {success: boolean; sessionId: string};

// ===================================
// TEST 1: IsReadonly<T, K>
// ===================================

type IsReadonly_Test1 = IsReadonly<TestReadonlyInterface, 'readonlyField'>;
type IsReadonly_Check1 = Expect<Equal<IsReadonly_Test1, true>>;

type IsReadonly_Test2 = IsReadonly<TestReadonlyInterface, 'mutableField'>;
type IsReadonly_Check2 = Expect<Equal<IsReadonly_Test2, false>>;

type IsReadonly_Test3 = IsReadonly<TestReadonlyInterface, 'optionalReadonly'>;
type IsReadonly_Check3 = Expect<Equal<IsReadonly_Test3, true>>;

type IsReadonly_Test4 = IsReadonly<TestReadonlyInterface, 'optionalMutable'>;
type IsReadonly_Check4 = Expect<Equal<IsReadonly_Test4, false>>;

// Debug
const isReadonlyDebug1: IsReadonly_Test1 = null!; // true
const isReadonlyDebug2: IsReadonly_Test2 = null!; // false
const isReadonlyDebug3: IsReadonly_Test3 = null!; // true
const isReadonlyDebug4: IsReadonly_Test4 = null!; // false

// ===================================
// TEST 2: AllKeys<T>
// ===================================

type JobTuple = [JobA, JobB, JobC];

type AllKeys_Test = AllKeys<JobTuple>;
type AllKeys_Expected = 'source' | 'sessionPath' | 'loginResult' | 'configPath';
type AllKeys_Check = Expect<Equal<AllKeys_Test, AllKeys_Expected>>;

// Individual key checks
type AllKeys_HasSource = 'source' extends AllKeys_Test ? true : false;
type AllKeys_HasSession = 'sessionPath' extends AllKeys_Test ? true : false;
type AllKeys_HasLogin = 'loginResult' extends AllKeys_Test ? true : false;
type AllKeys_HasConfig = 'configPath' extends AllKeys_Test ? true : false;

// Debug
const allKeysDebug: AllKeys_Test = null!; // "source" | "sessionPath" | "loginResult" | "configPath"
const allKeysHasSource: AllKeys_HasSource = null!; // true
const allKeysHasSession: AllKeys_HasSession = null!; // true
const allKeysHasLogin: AllKeys_HasLogin = null!; // true
const allKeysHasConfig: AllKeys_HasConfig = null!; // true

// ===================================
// TEST 3: HasKey<T, K>
// ===================================

interface TestTypeForHasKey {
    readonly source: string;
    sessionPath: string;
    optional?: number;
}

type HasKey_Test1 = HasKey<TestTypeForHasKey, 'source'>;
type HasKey_Check1 = Expect<Equal<HasKey_Test1, true>>;

type HasKey_Test2 = HasKey<TestTypeForHasKey, 'sessionPath'>;
type HasKey_Check2 = Expect<Equal<HasKey_Test2, true>>;

type HasKey_Test3 = HasKey<TestTypeForHasKey, 'optional'>;
type HasKey_Check3 = Expect<Equal<HasKey_Test3, true>>;

type HasKey_Test4 = HasKey<TestTypeForHasKey, 'nonExistent'>;
type HasKey_Check4 = Expect<Equal<HasKey_Test4, false>>;

// Type variety tests
type HasKey_StringKey = HasKey<TestTypeForHasKey, 'source'>;
type HasKey_NumberKey = HasKey<TestTypeForHasKey, 42>;
type HasKey_SymbolKey = HasKey<TestTypeForHasKey, symbol>;

// Debug
const hasKeyDebug1: HasKey_Test1 = null!; // true
const hasKeyDebug2: HasKey_Test2 = null!; // true
const hasKeyDebug3: HasKey_Test3 = null!; // true
const hasKeyDebug4: HasKey_Test4 = null!; // false
const hasKeyStringDebug: HasKey_StringKey = null!; // true
const hasKeyNumberDebug: HasKey_NumberKey = null!; // false
const hasKeySymbolDebug: HasKey_SymbolKey = null!; // false

// ===================================
// TEST 4: IsRequiredField<TContexts, K>
// ===================================

type IsRequiredField_Tuple = [JobA, JobB, JobC];

// 'source' - all have readonly → should be required (true)
type IsRequiredField_Test1 = IsRequiredField<IsRequiredField_Tuple, 'source'>;
type IsRequiredField_Check1 = Expect<Equal<IsRequiredField_Test1, true>>;

// 'sessionPath' - JobA produces (not readonly) → should NOT be required (false)
type IsRequiredField_Test2 = IsRequiredField<IsRequiredField_Tuple, 'sessionPath'>;
type IsRequiredField_Check2 = Expect<Equal<IsRequiredField_Test2, false>>;

// 'loginResult' - JobB produces (not readonly) → should NOT be required (false)
type IsRequiredField_Test3 = IsRequiredField<IsRequiredField_Tuple, 'loginResult'>;
type IsRequiredField_Check3 = Expect<Equal<IsRequiredField_Test3, false>>;

// 'nonExistent' - key doesn't exist → should be required (vacuous truth)
type IsRequiredField_Test4 = IsRequiredField<IsRequiredField_Tuple, 'nonExistent'>;
type IsRequiredField_Check4 = Expect<Equal<IsRequiredField_Test4, true>>;

// Edge cases - single job
type IsRequiredField_SingleTest1 = IsRequiredField<[JobA], 'source'>; // true (readonly)
type IsRequiredField_SingleTest2 = IsRequiredField<[JobA], 'sessionPath'>; // false (mutable)

// Debug
const isRequiredDebug1: IsRequiredField_Test1 = null!; // true
const isRequiredDebug2: IsRequiredField_Test2 = null!; // false
const isRequiredDebug3: IsRequiredField_Test3 = null!; // false
const isRequiredDebug4: IsRequiredField_Test4 = null!; // true
const isRequiredSingleDebug1: IsRequiredField_SingleTest1 = null!; // true
const isRequiredSingleDebug2: IsRequiredField_SingleTest2 = null!; // false

// ===================================
// TEST 5: ComputeRequiredFields<TContexts> (Logic Test)
// ===================================

type RealWorldTuple = [SyncJobContext, LoginJobContext, SaveJobContext];

type ComputeRequired_Test = ComputeRequiredFields<RealWorldTuple>;
type ComputeRequired_Expected = {readonly source: string};
type ComputeRequired_Check = Expect<Equal<ComputeRequired_Test, ComputeRequired_Expected>>;

// Component tests
type ComputeRequired_AllKeys = AllKeys<RealWorldTuple>;
type ComputeRequired_SourceReq = IsRequiredField<RealWorldTuple, 'source'>; // true
type ComputeRequired_TempDirReq = IsRequiredField<RealWorldTuple, 'tempDir'>; // false (SyncJob produces)
type ComputeRequired_LoginReq = IsRequiredField<RealWorldTuple, 'loginResult'>; // false (LoginJob produces)

// Debug
const computeRequiredDebug: ComputeRequired_Test = null!; // { readonly source: string }
const computeRequiredAllKeys: ComputeRequired_AllKeys = null!; // "source" | "tempDir" | "loginResult"
const computeRequiredSourceReq: ComputeRequired_SourceReq = null!; // true
const computeRequiredTempReq: ComputeRequired_TempDirReq = null!; // false
const computeRequiredLoginReq: ComputeRequired_LoginReq = null!; // false

// Edge cases
type ComputeRequired_Empty = ComputeRequiredFields<[]>;
type ComputeRequired_Single = ComputeRequiredFields<[SyncJobContext]>;
type ComputeRequired_SingleExpected = {readonly source: string};
type ComputeRequired_SingleCheck = Expect<Equal<ComputeRequired_Single, ComputeRequired_SingleExpected>>;

const computeRequiredEmptyDebug: ComputeRequired_Empty = null!; // {}
const computeRequiredSingleDebug: ComputeRequired_Single = null!; // { readonly source: string }

// ===================================
// TEST 6: ComputeRunContext<TContexts> (Practical Test)
// ===================================

type ComputeRun_Test = ComputeRunContext<RealWorldTuple>;

// Should include BaseContext fields
type ComputeRun_HasOperationId = 'operationId' extends keyof ComputeRun_Test ? true : false;
type ComputeRun_HasSource = 'source' extends keyof ComputeRun_Test ? true : false;
type ComputeRun_HasIsShadowRun = 'isShadowRun' extends keyof ComputeRun_Test ? true : false;

// Should NOT include fields that jobs produce
type ComputeRun_HasTempDir = 'tempDir' extends keyof ComputeRun_Test ? true : false; // false
type ComputeRun_HasLoginResult = 'loginResult' extends keyof ComputeRun_Test ? true : false; // false

// Debug
const computeRunDebug: ComputeRun_Test = null!; // BaseContext & additional required fields
const computeRunHasOpId: ComputeRun_HasOperationId = null!; // true
const computeRunHasSource: ComputeRun_HasSource = null!; // true
const computeRunHasShadow: ComputeRun_HasIsShadowRun = null!; // true
const computeRunHasTempDir: ComputeRun_HasTempDir = null!; // false
const computeRunHasLogin: ComputeRun_HasLoginResult = null!; // false

// ===================================
// SUMMARY EXPORT
// ===================================

export const TYPE_TESTS_SUMMARY = {
    IsReadonly: 'Tests readonly property detection',
    AllKeys: 'Tests union key extraction from tuple',
    HasKey: 'Tests key existence in types',
    IsRequiredField: 'Tests required field detection logic',
    ComputeRequiredFields: 'Tests required fields computation (logic only)',
    ComputeRunContext: 'Tests full run context with BaseContext integration'
} as const;

// If all tests pass, this should compile without errors
export const ALL_TESTS_PASSED = '✅ All context utility type tests passed!' as const;

// ===================================
// TEST 7: FindKeyType<TContexts, K> (Bug Fix Test)
// ===================================

// Helper do znajdowania typu klucza z pierwszego kontekstu gdzie występuje
type FindKeyType<TContexts extends readonly any[], K extends PropertyKey> = TContexts extends readonly [infer First, ...infer Rest]
    ? K extends keyof First
        ? First[K] // Znaleźliśmy klucz w First
        : Rest extends readonly any[]
        ? FindKeyType<Rest, K> // Szukaj dalej
        : never
    : never; // Nie znaleziono

// Test interfaces for realistic case
type SyncDependenciesJobContext_Real = BaseContext;

interface LoginJobContext_Real extends BaseContext {
    sessionValid?: boolean; // NIE readonly -> LoginJob ustawi
    checkSessionResult?: BinaryLoginResult; // NIE readonly -> LoginJob ustawi
}

interface UpdateSourceAccountJobContext_Real extends BaseContext {
    readonly sessionPath: string; // READONLY -> required na start
    readonly checkSessionResult?: BinaryLoginResult; // READONLY -> oczekuje od LoginJob
}

type RealisticTuple = [SyncDependenciesJobContext_Real, LoginJobContext_Real, UpdateSourceAccountJobContext_Real];

// Test FindKeyType
type FindKeyType_Test1 = FindKeyType<RealisticTuple, 'sessionPath'>;
type FindKeyType_Expected1 = string; // Powinno znaleźć string z UpdateSourceAccountJobContext
type FindKeyType_Check1 = Expect<Equal<FindKeyType_Test1, FindKeyType_Expected1>>;

type FindKeyType_Test2 = FindKeyType<RealisticTuple, 'sessionValid'>;
type FindKeyType_Expected2 = boolean | undefined; // Z LoginJobContext
type FindKeyType_Check2 = Expect<Equal<FindKeyType_Test2, FindKeyType_Expected2>>;

type FindKeyType_Test3 = FindKeyType<RealisticTuple, 'source'>;
type FindKeyType_Expected3 = Source; // Z BaseContext (pierwszy context)
type FindKeyType_Check3 = Expect<Equal<FindKeyType_Test3, FindKeyType_Expected3>>;

type FindKeyType_Test4 = FindKeyType<RealisticTuple, 'nonExistent'>;
type FindKeyType_Expected4 = never; // Nie istnieje
type FindKeyType_Check4 = Expect<Equal<FindKeyType_Test4, FindKeyType_Expected4>>;

// Debug
const findKeyDebug1: FindKeyType_Test1 = null!; // string (nie never!)
const findKeyDebug2: FindKeyType_Test2 = null!; // boolean | undefined
const findKeyDebug3: FindKeyType_Test3 = null!; // Source
const findKeyDebug4: FindKeyType_Test4 = null!; // never

// ===================================
// TEST 8: Fixed ComputeRunContext (Anti-Never Test)
// ===================================

// Poprawiony ComputeRunContext z FindKeyType
type ComputeRunContextFixed<TContexts extends readonly any[]> = BaseContext & {
    readonly [K in AllKeys<TContexts> as K extends keyof BaseContext ? never : IsRequiredField<TContexts, K> extends true ? K : never]: FindKeyType<TContexts, K>; // ✅ FindKeyType zamiast union logic
};

type FixedRunContext_Test = ComputeRunContextFixed<RealisticTuple>;

// Should have sessionPath as string (not never!)
type FixedRunContext_SessionPathType = FixedRunContext_Test['sessionPath'];
type FixedRunContext_SessionPathCheck = Expect<Equal<FixedRunContext_SessionPathType, string>>;

// Should NOT have sessionValid or checkSessionResult (not required)
type FixedRunContext_HasSessionValid = 'sessionValid' extends keyof FixedRunContext_Test ? true : false;
type FixedRunContext_HasCheckResult = 'checkSessionResult' extends keyof FixedRunContext_Test ? true : false;

// Should have BaseContext fields
type FixedRunContext_HasOperationId = 'operationId' extends keyof FixedRunContext_Test ? true : false;

// Debug
const fixedRunContextDebug: FixedRunContext_Test = null!;
// Powinno pokazać: BaseContext & { readonly sessionPath: string }

const sessionPathTypeDebug: FixedRunContext_SessionPathType = null!; // string
const hasSessionValidDebug: FixedRunContext_HasSessionValid = null!; // false
const hasCheckResultDebug: FixedRunContext_HasCheckResult = null!; // false
const hasOperationIdDebug: FixedRunContext_HasOperationId = null!; // true

// ===================================
// TEST 9: Real World SetCredentialsRun Context
// ===================================

type SetCredentialsRunContext_Fixed = ComputeRunContextFixed<RealisticTuple>;

// Explicit test of what should be required:
// - sessionPath: tylko UpdateSourceAccount ma, jako readonly -> REQUIRED
// - sessionValid: tylko LoginJob ma, nie readonly -> NIE required
// - checkSessionResult: LoginJob (nie readonly) vs UpdateSourceAccount (readonly) -> NIE required

type SetCredentials_RequiredFields = ComputeRequiredFields<RealisticTuple>;
type SetCredentials_ExpectedRequired = {readonly sessionPath: string};
// type SetCredentials_RequiredCheck = Expect<Equal<SetCredentials_RequiredFields, SetCredentials_ExpectedRequired>>;

// Full context should be BaseContext + required fields
type SetCredentials_FullExpected = BaseContext & {readonly sessionPath: string};

// Test individual required field checks
type SetCredentials_SessionPathReq = IsRequiredField<RealisticTuple, 'sessionPath'>; // true
type SetCredentials_SessionValidReq = IsRequiredField<RealisticTuple, 'sessionValid'>; // false
type SetCredentials_CheckResultReq = IsRequiredField<RealisticTuple, 'checkSessionResult'>; // false

// Debug
const setCredentialsDebug: SetCredentialsRunContext_Fixed = null!;
// BaseContext & { readonly sessionPath: string }

const setCredentialsRequiredDebug: SetCredentials_RequiredFields = null!;
// { readonly sessionPath: string }

const sessionPathReqDebug: SetCredentials_SessionPathReq = null!; // true
const sessionValidReqDebug: SetCredentials_SessionValidReq = null!; // false
const checkResultReqDebug: SetCredentials_CheckResultReq = null!; // false

// ===================================
// ANTI-REGRESSION TEST: Never Detection
// ===================================

// This should catch if we accidentally return 'never' for any valid field
type AntiNever_SessionPath = FindKeyType<RealisticTuple, 'sessionPath'>;
type AntiNever_Source = FindKeyType<RealisticTuple, 'source'>;

// These should NOT be never
type AntiNever_Check1 = AntiNever_SessionPath extends never ? false : true;
type AntiNever_Check2 = AntiNever_Source extends never ? false : true;

type AntiNever_Assert1 = Expect<Equal<AntiNever_Check1, true>>; // sessionPath is not never
type AntiNever_Assert2 = Expect<Equal<AntiNever_Check2, true>>; // source is not never

const antiNeverDebug1: AntiNever_Check1 = null!; // true (not never!)
const antiNeverDebug2: AntiNever_Check2 = null!; // true (not never!)
