import {TriggeredBy} from '../../telemetry/scraperService/scraperServiceEvents';
import {Command, ShadowModeTask, Source} from '../../types';

export interface BaseContext {
    source: Source;
    command: Command;
    shadowModeTask?: ShadowModeTask;
    isShadowRun: boolean;
    forceRunStart: boolean;
    operationId: string;
    triggeredBy: TriggeredBy;
}

// === UTILITY TYPES ===

/**
 * Checks if a property K in type T is readonly
 */
export type IsReadonly<T, K extends keyof T> = (<F>() => F extends {[Q in K]: T[K]} ? 1 : 2) extends <F>() => F extends {-readonly [Q in K]: T[K]} ? 1 : 2 ? false : true;

/**
 * Extracts all possible keys from a tuple of types using distributive conditional
 */
export type AllKeys<T extends readonly any[]> = T[number] extends infer U ? (U extends any ? keyof U : never) : never;

/**
 * Checks if type T has key K
 */
export type HasKey<T, K extends PropertyKey> = K extends keyof T ? true : false;

/**
 * Determines if a field K is required as input by checking if ALL types that contain K have it as readonly.
 * If any type has K as mutable (non-readonly), then K is not required as input (will be produced by a job).
 */
export type IsRequiredField<TContexts extends readonly any[], K extends PropertyKey> = TContexts extends readonly [infer First, ...infer Rest]
    ? K extends keyof First
        ? IsReadonly<First, K> extends true
            ? IsRequiredField<Rest, K> // First has readonly K - check rest
            : false // First has mutable K - not required as input
        : IsRequiredField<Rest, K> // First doesn't have K - check rest
    : true; // Went through all types - required as input

type FindKeyType<TContexts extends readonly any[], K extends PropertyKey> = TContexts extends readonly [infer First, ...infer Rest]
    ? K extends keyof First
        ? First[K] // Znaleźliśmy klucz w First
        : Rest extends readonly any[]
        ? FindKeyType<Rest, K> // Szukaj dalej
        : never
    : never; // Nie znaleziono

// Poprawiony ComputeRunContext:
export type ComputeRunContext<TContexts extends readonly any[]> = BaseContext & {
    readonly [K in AllKeys<TContexts> as K extends keyof BaseContext ? never : IsRequiredField<TContexts, K> extends true ? K : never]: FindKeyType<TContexts, K>; // ✅ Używamy FindKeyType zamiast union logic
};

// /**
//  * Computes the required input context for a Run by:
//  * 1. Including BaseContext (always required)
//  * 2. Adding only those fields that are readonly in ALL job contexts where they appear
//  * 3. Excluding fields already in BaseContext to avoid conflicts
//  */
// export type ComputeRunContext<TContexts extends readonly any[]> = BaseContext & {
//     readonly [K in AllKeys<TContexts> as K extends keyof BaseContext
//         ? never // Skip fields already in BaseContext
//         : IsRequiredField<TContexts, K> extends true
//         ? K
//         : never]: TContexts[number] extends infer U ? (K extends keyof U ? U[K] : never) : never;
// };

/**
 * Helper type to compute only the additional required fields (without BaseContext)
 * Useful for testing the logic without BaseContext interference
 */
export type ComputeRequiredFields<TContexts extends readonly any[]> = {
    readonly [K in AllKeys<TContexts> as IsRequiredField<TContexts, K> extends true ? K : never]: TContexts[number] extends infer U
        ? K extends keyof U
            ? U[K]
            : never
        : never;
};
